import React from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
} from "react-native";
import {
  responsiveFontSize,
  responsiveHeight,
  responsiveWidth,
} from "react-native-responsive-dimensions";
import Colors from "../../styles/Colors";
import MyHeader from "../../components/MyHeader";

const OffersPage = ({ navigation }) => {
  const offers = [
    {
      id: "1",
      title: "25% OFF on Health Packages",
      description: "Save big on popular tests.",
      validTill: "31st Aug 2025",
      status: "Active",
      labs: [
        { id: "L1", name: "Apollo Diagnostics", logo: "https://via.placeholder.com/60" },
        { id: "L2", name: "<PERSON>Labs", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T1", name: "Full Body Checkup", originalPrice: 1999, discountPrice: 1499 },
        { id: "T2", name: "Thyroid Panel", originalPrice: 899, discountPrice: 699 },
      ],
    },
    {
      id: "2",
      title: "Flat ₹200 OFF",
      description: "Applicable on diabetes screening",
      validTill: "15th Sep 2025",
      status: "Active",
      labs: [
        { id: "L3", name: "SRL Diagnostics", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T3", name: "Diabetes Screening", originalPrice: 999, discountPrice: 799 },
      ],
    },
    {
      id: "3",
      title: "50% OFF on Women Health",
      description: "Special offer for women wellness.",
      validTill: "10th Sep 2025",
      status: "Limited Time",
      labs: [
        { id: "L4", name: "Metropolis Labs", logo: "https://via.placeholder.com/60" },
        { id: "L5", name: "Healthians", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T4", name: "PCOD Test", originalPrice: 1500, discountPrice: 750 },
        { id: "T5", name: "Hormone Panel", originalPrice: 1800, discountPrice: 900 },
      ],
    },
    {
      id: "4",
      title: "Free Consultation",
      description: "On booking above ₹999.",
      validTill: "30th Sep 2025",
      status: "Active",
      labs: [
        { id: "L6", name: "Apollo Diagnostics", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T6", name: "Liver Function Test", originalPrice: 1200, discountPrice: 1200 },
      ],
    },
    {
      id: "5",
      title: "20% OFF on Thyroid Tests",
      description: "Valid on selected labs.",
      validTill: "5th Oct 2025",
      status: "Active",
      labs: [
        { id: "L7", name: "Lal PathLabs", logo: "https://via.placeholder.com/60" },
        { id: "L8", name: "SRL Diagnostics", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T7", name: "TSH Test", originalPrice: 500, discountPrice: 400 },
        { id: "T8", name: "T3 & T4", originalPrice: 800, discountPrice: 640 },
      ],
    },
    {
      id: "6",
      title: "30% OFF on Senior Citizen Packages",
      description: "Discounted health checkups for 60+ age.",
      validTill: "31st Dec 2025",
      status: "Limited Time",
      labs: [
        { id: "L9", name: "Metropolis Labs", logo: "https://via.placeholder.com/60" },
      ],
      tests: [
        { id: "T9", name: "Senior Health Package", originalPrice: 2500, discountPrice: 1750 },
      ],
    },
  ];

  const renderItem = ({ item }) => (
    <View style={styles.card}>
      {/* Offer Title */}
      <Text style={styles.offerTitle}>{item.title}</Text>
      <Text style={styles.offerDesc}>{item.description}</Text>

      {/* Labs */}
      <Text style={styles.sectionTitle}>Applicable Labs:</Text>
      <View style={styles.labRow}>
        {item.labs.map((lab) => (
          <View key={lab.id} style={styles.labBox}>
            <Image source={{ uri: lab.logo }} style={styles.labLogo} />
            <Text style={styles.labName}>{lab.name}</Text>
          </View>
        ))}
      </View>

      {/* Tests */}
      <Text style={styles.sectionTitle}>Applicable Tests:</Text>
      {item.tests.map((test) => (
        <View key={test.id} style={styles.testRow}>
          <Text style={styles.testName}>{test.name}</Text>
          <Text style={styles.originalPrice}>₹{test.originalPrice}</Text>
          <Text style={styles.discountPrice}>₹{test.discountPrice}</Text>
        </View>
      ))}

      {/* Validity */}
      <Text style={styles.validity}>Valid till: {item.validTill}</Text>

      {/* Book Now */}
      <TouchableOpacity
        style={styles.bookBtn}
        onPress={() =>
          navigation.navigate("BookingConfirmation", {
            offerId: item.id,
            labs: item.labs,
            tests: item.tests,
          })
        }
      >
        <Text style={styles.bookText}>Book Now</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <MyHeader title="Offers" onBackPress={() => navigation.goBack()} />
      <FlatList
        data={offers}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: responsiveWidth(4) }}
      />
    </View>
  );
};

export default OffersPage;

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.white,
    borderRadius: responsiveWidth(3),
    padding: responsiveWidth(4),
    marginBottom: responsiveHeight(2),
    elevation: 3,
  },
  offerTitle: {
    fontSize: responsiveFontSize(2.2),
    fontWeight: "bold",
    color: Colors.primary,
    marginBottom: responsiveHeight(0.5),
  },
  offerDesc: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.tertiary,
    marginBottom: responsiveHeight(1.5),
  },
  sectionTitle: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: "600",
    color: Colors.black,
    marginTop: responsiveHeight(0.5),
    marginBottom: responsiveHeight(0.5),
  },
  labRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: responsiveHeight(1),
  },
  labBox: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: responsiveWidth(4),
    marginBottom: responsiveHeight(1),
  },
  labLogo: {
    width: responsiveWidth(10),
    height: responsiveWidth(10),
    borderRadius: responsiveWidth(2),
    marginRight: responsiveWidth(2),
  },
  labName: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
  },
  testRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: responsiveHeight(0.5),
  },
  testName: {
    flex: 1,
    fontSize: responsiveFontSize(1.8),
    color: Colors.black,
  },
  originalPrice: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.grey,
    textDecorationLine: "line-through",
    marginRight: responsiveWidth(2),
  },
  discountPrice: {
    fontSize: responsiveFontSize(1.8),
    color: Colors.success,
    fontWeight: "bold",
  },
  validity: {
    fontSize: responsiveFontSize(1.6),
    color: Colors.tertiary,
    marginTop: responsiveHeight(1),
    marginBottom: responsiveHeight(1),
  },
  bookBtn: {
    backgroundColor: Colors.primary,
    borderRadius: responsiveWidth(2),
    paddingVertical: responsiveHeight(1),
    alignItems: "center",
  },
  bookText: {
    fontSize: responsiveFontSize(1.8),
    fontWeight: "bold",
    color: Colors.white,
  },
});